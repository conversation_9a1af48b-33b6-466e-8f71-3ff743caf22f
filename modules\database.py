# نظام قاعدة البيانات للوكيل البرمجي
import sqlite3
import hashlib
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import os
from .logger import logger

class ArticleDatabase:
    """قاعدة بيانات إدارة المقالات والتكرار"""
    
    def __init__(self, db_path: str = "data/articles.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # جدول المقالات المنشورة
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS published_articles (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        title TEXT NOT NULL,
                        content TEXT,
                        content_hash TEXT UNIQUE NOT NULL,
                        semantic_hash TEXT NOT NULL,
                        source_url TEXT,
                        source_type TEXT,
                        blogger_url TEXT,
                        telegram_message_id INTEGER,
                        keywords TEXT,
                        category TEXT,
                        dialect TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        published_at TIMESTAMP,
                        view_count INTEGER DEFAULT 0,
                        engagement_score REAL DEFAULT 0.0
                    )
                ''')

                # إضافة عمود content إذا لم يكن موجوداً (للتوافق مع قواعد البيانات القديمة)
                try:
                    cursor.execute('ALTER TABLE published_articles ADD COLUMN content TEXT')
                    logger.info("✅ تم إضافة عمود content إلى جدول published_articles")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" in str(e).lower():
                        pass  # العمود موجود بالفعل
                    else:
                        logger.warning(f"⚠️ تحذير في تحديث بنية الجدول: {e}")
                
                # جدول المصادر المراقبة
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS monitored_sources (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        source_url TEXT UNIQUE NOT NULL,
                        source_type TEXT NOT NULL,
                        last_checked TIMESTAMP,
                        success_count INTEGER DEFAULT 0,
                        error_count INTEGER DEFAULT 0,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول سجل الأخطاء
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS error_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        error_type TEXT NOT NULL,
                        error_message TEXT,
                        stack_trace TEXT,
                        source_url TEXT,
                        retry_count INTEGER DEFAULT 0,
                        resolved BOOLEAN DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول إحصائيات الأداء
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS performance_stats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        date DATE UNIQUE NOT NULL,
                        articles_processed INTEGER DEFAULT 0,
                        articles_published INTEGER DEFAULT 0,
                        api_calls_gemini INTEGER DEFAULT 0,
                        api_calls_telegram INTEGER DEFAULT 0,
                        api_calls_blogger INTEGER DEFAULT 0,
                        errors_count INTEGER DEFAULT 0,
                        uptime_hours REAL DEFAULT 0.0
                    )
                ''')
                
                # جدول الفيديوهات المعالجة
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS processed_videos (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        video_id TEXT UNIQUE NOT NULL,
                        title TEXT NOT NULL,
                        channel_id TEXT,
                        channel_name TEXT,
                        duration INTEGER,
                        published_date TIMESTAMP,
                        processed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        transcript_length INTEGER,
                        news_extracted INTEGER DEFAULT 0,
                        article_id INTEGER,
                        FOREIGN KEY (article_id) REFERENCES published_articles (id)
                    )
                ''')

                # جدول قرارات الموافقة على الفيديوهات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS video_approvals (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        approval_id TEXT UNIQUE NOT NULL,
                        video_id TEXT NOT NULL,
                        video_title TEXT,
                        channel_name TEXT,
                        approved BOOLEAN NOT NULL,
                        reason TEXT,
                        request_time TIMESTAMP,
                        decision_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # جدول تحليل النصوص المستخرجة
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS video_transcripts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        video_id TEXT NOT NULL,
                        transcript_text TEXT NOT NULL,
                        language TEXT,
                        main_news_count INTEGER DEFAULT 0,
                        additional_info_count INTEGER DEFAULT 0,
                        processing_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (video_id) REFERENCES processed_videos (video_id)
                    )
                ''')

                # جدول فحص جودة Whisper
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS whisper_quality_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TIMESTAMP NOT NULL,
                        quality_score REAL NOT NULL,
                        quality_level TEXT NOT NULL,
                        is_acceptable BOOLEAN NOT NULL,
                        video_id TEXT,
                        video_title TEXT,
                        details TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # جدول الإشعارات العامة
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS public_notifications (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        type TEXT NOT NULL,
                        video_id TEXT,
                        title TEXT,
                        url TEXT,
                        duration INTEGER,
                        channel TEXT,
                        processed_at TIMESTAMP,
                        extracted_text_length INTEGER,
                        status TEXT,
                        message TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # فهارس لتحسين الأداء
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_content_hash ON published_articles(content_hash)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_semantic_hash ON published_articles(semantic_hash)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_published_date ON published_articles(published_at)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_source_type ON published_articles(source_type)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_video_id ON processed_videos(video_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_approval_id ON video_approvals(approval_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_transcript_video_id ON video_transcripts(video_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_whisper_quality_video_id ON whisper_quality_logs(video_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_whisper_quality_timestamp ON whisper_quality_logs(timestamp)')
                
                conn.commit()
                logger.info("✅ تم إنشاء قاعدة البيانات بنجاح")
                
        except Exception as e:
            logger.error("❌ فشل في إنشاء قاعدة البيانات", e)
            raise
    
    def generate_content_hash(self, content: str) -> str:
        """إنشاء تجزئة للمحتوى"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    def generate_semantic_hash(self, title: str, keywords: List[str]) -> str:
        """إنشاء تجزئة دلالية للمحتوى"""
        # دمج العنوان والكلمات المفتاحية لإنشاء بصمة دلالية
        semantic_content = f"{title.lower()} {' '.join(keywords).lower()}"
        return hashlib.md5(semantic_content.encode('utf-8')).hexdigest()
    
    def is_duplicate_content(self, content: str, title: str, keywords: List[str]) -> Tuple[bool, str]:
        """فحص تكرار المحتوى"""
        content_hash = self.generate_content_hash(content)
        semantic_hash = self.generate_semantic_hash(title, keywords)
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # فحص التطابق التام للمحتوى
                cursor.execute(
                    "SELECT id, title FROM published_articles WHERE content_hash = ?",
                    (content_hash,)
                )
                exact_match = cursor.fetchone()
                
                if exact_match:
                    return True, f"تطابق تام للمحتوى مع المقال: {exact_match[1]}"
                
                # فحص التشابه الدلالي (خلال آخر 30 يوم)
                thirty_days_ago = datetime.now() - timedelta(days=30)
                cursor.execute(
                    "SELECT id, title FROM published_articles WHERE semantic_hash = ? AND published_at > ?",
                    (semantic_hash, thirty_days_ago)
                )
                semantic_match = cursor.fetchone()
                
                if semantic_match:
                    return True, f"تشابه دلالي مع المقال: {semantic_match[1]}"
                
                return False, "المحتوى فريد"
                
        except Exception as e:
            logger.error("❌ فشل في فحص تكرار المحتوى", e)
            return False, "خطأ في فحص التكرار"
    
    def save_article(self, article_data: Dict) -> int:
        """حفظ مقال جديد في قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                content_hash = self.generate_content_hash(article_data['content'])
                semantic_hash = self.generate_semantic_hash(
                    article_data['title'], 
                    article_data.get('keywords', [])
                )
                
                cursor.execute('''
                    INSERT INTO published_articles 
                    (title, content_hash, semantic_hash, source_url, source_type, 
                     blogger_url, telegram_message_id, keywords, category, dialect, published_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    article_data['title'],
                    content_hash,
                    semantic_hash,
                    article_data.get('source_url'),
                    article_data.get('source_type'),
                    article_data.get('blogger_url'),
                    article_data.get('telegram_message_id'),
                    json.dumps(article_data.get('keywords', []), ensure_ascii=False),
                    article_data.get('category'),
                    article_data.get('dialect'),
                    datetime.now()
                ))
                
                article_id = cursor.lastrowid
                conn.commit()
                
                logger.log_content_processing(
                    "مقال جديد", 
                    article_data.get('source_url', 'غير محدد'), 
                    "حفظ في قاعدة البيانات", 
                    "نجح"
                )
                
                return article_id
                
        except Exception as e:
            logger.error("❌ فشل في حفظ المقال", e)
            raise
    
    def update_article_urls(self, article_id: int, blogger_url: str = None, telegram_message_id: int = None):
        """تحديث روابط المقال بعد النشر"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                updates = []
                params = []
                
                if blogger_url:
                    updates.append("blogger_url = ?")
                    params.append(blogger_url)
                
                if telegram_message_id:
                    updates.append("telegram_message_id = ?")
                    params.append(telegram_message_id)
                
                if updates:
                    params.append(article_id)
                    query = f"UPDATE published_articles SET {', '.join(updates)} WHERE id = ?"
                    cursor.execute(query, params)
                    conn.commit()
                    
                    logger.info(f"✅ تم تحديث روابط المقال رقم {article_id}")
                
        except Exception as e:
            logger.error("❌ فشل في تحديث روابط المقال", e)
    
    def get_recent_articles(self, limit: int = 10) -> List[Dict]:
        """الحصول على آخر المقالات المنشورة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT title, source_url, blogger_url, category, published_at
                    FROM published_articles 
                    ORDER BY published_at DESC 
                    LIMIT ?
                ''', (limit,))
                
                articles = []
                for row in cursor.fetchall():
                    articles.append({
                        'title': row[0],
                        'source_url': row[1],
                        'blogger_url': row[2],
                        'category': row[3],
                        'published_at': row[4]
                    })
                
                return articles
                
        except Exception as e:
            logger.error("❌ فشل في جلب المقالات الأخيرة", e)
            return []
    
    def log_error(self, error_type: str, error_message: str, source_url: str = None, stack_trace: str = None):
        """تسجيل خطأ في قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO error_log (error_type, error_message, source_url, stack_trace)
                    VALUES (?, ?, ?, ?)
                ''', (error_type, error_message, source_url, stack_trace))
                
                conn.commit()
                
        except Exception as e:
            logger.error("❌ فشل في تسجيل الخطأ في قاعدة البيانات", e)
    
    def update_performance_stats(self, date: str = None, **stats):
        """تحديث إحصائيات الأداء"""
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # محاولة تحديث السجل الموجود
                cursor.execute(
                    "SELECT id FROM performance_stats WHERE date = ?",
                    (date,)
                )
                
                if cursor.fetchone():
                    # تحديث السجل الموجود
                    updates = []
                    params = []
                    
                    for key, value in stats.items():
                        updates.append(f"{key} = {key} + ?")
                        params.append(value)
                    
                    if updates:
                        params.append(date)
                        query = f"UPDATE performance_stats SET {', '.join(updates)} WHERE date = ?"
                        cursor.execute(query, params)
                else:
                    # إنشاء سجل جديد
                    columns = ["date"] + list(stats.keys())
                    values = [date] + list(stats.values())
                    placeholders = ", ".join(["?"] * len(values))
                    
                    query = f"INSERT INTO performance_stats ({', '.join(columns)}) VALUES ({placeholders})"
                    cursor.execute(query, values)
                
                conn.commit()

        except Exception as e:
            logger.error("❌ فشل في تحديث إحصائيات الأداء", e)

    def save_learning_data(self, learning_data: Dict):
        """حفظ بيانات التعلم من دورات العمل"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # إنشاء جدول التعلم إذا لم يكن موجوداً
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS learning_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        cycle_timestamp TIMESTAMP NOT NULL,
                        performance_score INTEGER,
                        decisions_made TEXT,
                        results_achieved TEXT,
                        lessons_learned TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                cursor.execute('''
                    INSERT INTO learning_data
                    (cycle_timestamp, performance_score, decisions_made, results_achieved, lessons_learned)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    learning_data['cycle_timestamp'],
                    learning_data['performance_score'],
                    json.dumps(learning_data.get('decisions_made', {}), ensure_ascii=False),
                    json.dumps(learning_data.get('results_achieved', {}), ensure_ascii=False),
                    json.dumps(learning_data.get('lessons_learned', []), ensure_ascii=False)
                ))

                conn.commit()
                logger.info("💾 تم حفظ بيانات التعلم بنجاح")

        except Exception as e:
            logger.error("❌ فشل في حفظ بيانات التعلم", e)

    def is_video_processed(self, video_id: str) -> bool:
        """فحص ما إذا كان الفيديو تم معالجته من قبل"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT COUNT(*) FROM processed_videos WHERE video_id = ?",
                    (video_id,)
                )
                count = cursor.fetchone()[0]
                return count > 0

        except Exception as e:
            logger.error(f"❌ خطأ في فحص معالجة الفيديو: {e}")
            return False

    def save_processed_video(self, video_data: Dict) -> Optional[int]:
        """حفظ بيانات الفيديو المعالج"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO processed_videos
                    (video_id, title, channel_id, channel_name, duration, published_date,
                     transcript_length, news_extracted, article_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    video_data.get('video_id'),
                    video_data.get('title'),
                    video_data.get('channel_id'),
                    video_data.get('channel_name'),
                    video_data.get('duration'),
                    video_data.get('published_date'),
                    video_data.get('transcript_length', 0),
                    video_data.get('news_extracted', 0),
                    video_data.get('article_id')
                ))

                video_record_id = cursor.lastrowid
                conn.commit()

                logger.info(f"✅ تم حفظ بيانات الفيديو المعالج: {video_data.get('title')}")
                return video_record_id

        except Exception as e:
            logger.error(f"❌ فشل في حفظ بيانات الفيديو: {e}")
            return None

    def log_video_approval_decision(self, decision_data: Dict) -> bool:
        """تسجيل قرار الموافقة على الفيديو"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO video_approvals
                    (approval_id, video_id, video_title, channel_name, approved, reason, request_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    decision_data.get('approval_id'),
                    decision_data.get('video_id'),
                    decision_data.get('video_title'),
                    decision_data.get('channel_name'),
                    decision_data.get('approved'),
                    decision_data.get('reason'),
                    decision_data.get('request_time')
                ))

                conn.commit()
                logger.info(f"✅ تم تسجيل قرار الموافقة: {decision_data.get('approval_id')}")
                return True

        except Exception as e:
            logger.error(f"❌ فشل في تسجيل قرار الموافقة: {e}")
            return False

    def save_video_transcript(self, video_id: str, transcript_data: Dict) -> bool:
        """حفظ النص المستخرج من الفيديو"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO video_transcripts
                    (video_id, transcript_text, language, main_news_count, additional_info_count)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    video_id,
                    transcript_data.get('transcript_text'),
                    transcript_data.get('language'),
                    transcript_data.get('main_news_count', 0),
                    transcript_data.get('additional_info_count', 0)
                ))

                conn.commit()
                logger.info(f"✅ تم حفظ النص المستخرج للفيديو: {video_id}")
                return True

        except Exception as e:
            logger.error(f"❌ فشل في حفظ النص المستخرج: {e}")
            return False

    def get_video_processing_stats(self, days: int = 7) -> Dict:
        """الحصول على إحصائيات معالجة الفيديوهات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # إحصائيات عامة
                cursor.execute('''
                    SELECT
                        COUNT(*) as total_videos,
                        SUM(news_extracted) as total_news,
                        AVG(transcript_length) as avg_transcript_length,
                        COUNT(DISTINCT channel_name) as unique_channels
                    FROM processed_videos
                    WHERE processed_date >= datetime('now', '-{} days')
                '''.format(days))

                stats = cursor.fetchone()

                # إحصائيات الموافقات
                cursor.execute('''
                    SELECT
                        COUNT(*) as total_approvals,
                        SUM(CASE WHEN approved = 1 THEN 1 ELSE 0 END) as approved_count,
                        SUM(CASE WHEN approved = 0 THEN 1 ELSE 0 END) as rejected_count
                    FROM video_approvals
                    WHERE decision_time >= datetime('now', '-{} days')
                '''.format(days))

                approval_stats = cursor.fetchone()

                return {
                    'total_videos_processed': stats[0] or 0,
                    'total_news_extracted': stats[1] or 0,
                    'average_transcript_length': stats[2] or 0,
                    'unique_channels': stats[3] or 0,
                    'total_approval_requests': approval_stats[0] or 0,
                    'approved_videos': approval_stats[1] or 0,
                    'rejected_videos': approval_stats[2] or 0,
                    'approval_rate': (approval_stats[1] / approval_stats[0] * 100) if approval_stats[0] > 0 else 0
                }

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات الفيديوهات: {e}")
            return {}

    def get_channel_performance(self, days: int = 30) -> List[Dict]:
        """الحصول على أداء القنوات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT
                        channel_name,
                        COUNT(*) as videos_processed,
                        SUM(news_extracted) as news_extracted,
                        AVG(transcript_length) as avg_transcript_length,
                        MAX(processed_date) as last_processed
                    FROM processed_videos
                    WHERE processed_date >= datetime('now', '-{} days')
                    GROUP BY channel_name
                    ORDER BY videos_processed DESC
                '''.format(days))

                results = cursor.fetchall()

                channels = []
                for row in results:
                    channels.append({
                        'channel_name': row[0],
                        'videos_processed': row[1],
                        'news_extracted': row[2] or 0,
                        'avg_transcript_length': row[3] or 0,
                        'last_processed': row[4],
                        'efficiency': (row[2] / row[1] * 100) if row[1] > 0 else 0
                    })

                return channels

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على أداء القنوات: {e}")
            return []

    def get_stats_summary(self, days: int = 7) -> Dict:
        """الحصول على ملخص إحصائيات الأداء"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # إحصائيات آخر فترة محددة
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
                
                cursor.execute('''
                    SELECT 
                        SUM(articles_processed) as total_processed,
                        SUM(articles_published) as total_published,
                        SUM(api_calls_gemini) as total_gemini_calls,
                        SUM(errors_count) as total_errors,
                        AVG(uptime_hours) as avg_uptime
                    FROM performance_stats 
                    WHERE date >= ?
                ''', (start_date,))
                
                stats = cursor.fetchone()
                
                # عدد المقالات المنشورة اليوم
                today = datetime.now().strftime('%Y-%m-%d')
                cursor.execute(
                    "SELECT COUNT(*) FROM published_articles WHERE DATE(published_at) = ?",
                    (today,)
                )
                today_articles = cursor.fetchone()[0]
                
                return {
                    'period_days': days,
                    'total_processed': stats[0] or 0,
                    'total_published': stats[1] or 0,
                    'total_gemini_calls': stats[2] or 0,
                    'total_errors': stats[3] or 0,
                    'avg_uptime_hours': round(stats[4] or 0, 2),
                    'today_articles': today_articles
                }
                
        except Exception as e:
            logger.error("❌ فشل في جلب ملخص الإحصائيات", e)
            return {}

    def log_whisper_quality_check(self, quality_data: Dict) -> bool:
        """تسجيل نتائج فحص جودة Whisper"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # التأكد من وجود القيم المطلوبة وتوفير قيم افتراضية
                timestamp = quality_data.get('timestamp') or datetime.now().isoformat()
                quality_score = quality_data.get('quality_score')
                quality_level = quality_data.get('quality_level')
                is_acceptable = quality_data.get('is_acceptable')

                # التحقق من القيم المطلوبة وتنظيفها
                if quality_score is None:
                    quality_score = 0.0
                else:
                    # محاولة تحويل إلى رقم
                    try:
                        quality_score = float(quality_score)
                    except (ValueError, TypeError):
                        quality_score = 0.0
                        logger.warning(f"⚠️ قيمة quality_score غير صحيحة، تم تعيينها إلى 0.0")

                if quality_level is None or quality_level == '':
                    quality_level = 'غير محدد'
                else:
                    quality_level = str(quality_level)

                if is_acceptable is None:
                    is_acceptable = False
                else:
                    # تحويل إلى boolean
                    if isinstance(is_acceptable, str):
                        is_acceptable = is_acceptable.lower() in ['true', '1', 'yes', 'نعم']
                    else:
                        is_acceptable = bool(is_acceptable)

                # تحويل is_acceptable إلى integer للـ SQLite
                is_acceptable_int = 1 if is_acceptable else 0

                cursor.execute('''
                    INSERT INTO whisper_quality_logs (
                        timestamp, quality_score, quality_level, is_acceptable,
                        video_id, video_title, details
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    timestamp,
                    float(quality_score),
                    str(quality_level),
                    is_acceptable_int,
                    quality_data.get('video_id', ''),
                    quality_data.get('video_title', ''),
                    json.dumps(quality_data, ensure_ascii=False)
                ))

                conn.commit()
                return True

        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل فحص جودة Whisper: {e}")
            logger.debug(f"بيانات Whisper المرسلة: {quality_data}")

            # محاولة إصلاح الجدول إذا كانت المشكلة في البنية
            if "NOT NULL constraint failed" in str(e):
                logger.info("🔧 محاولة إصلاح جدول whisper_quality_logs...")
                try:
                    self._fix_whisper_quality_table()
                    # محاولة الإدراج مرة أخرى
                    return self.log_whisper_quality_check(quality_data)
                except Exception as fix_error:
                    logger.error(f"❌ فشل في إصلاح الجدول: {fix_error}")

            return False

    def _fix_whisper_quality_table(self):
        """إصلاح جدول whisper_quality_logs"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # حذف الجدول القديم إذا كان موجوداً
                cursor.execute('DROP TABLE IF EXISTS whisper_quality_logs_backup')

                # إنشاء نسخة احتياطية من البيانات الموجودة
                cursor.execute('''
                    CREATE TABLE whisper_quality_logs_backup AS
                    SELECT * FROM whisper_quality_logs
                ''')

                # حذف الجدول القديم
                cursor.execute('DROP TABLE whisper_quality_logs')

                # إعادة إنشاء الجدول بالبنية الصحيحة
                cursor.execute('''
                    CREATE TABLE whisper_quality_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        quality_score REAL NOT NULL DEFAULT 0.0,
                        quality_level TEXT NOT NULL DEFAULT 'غير محدد',
                        is_acceptable BOOLEAN NOT NULL DEFAULT 0,
                        video_id TEXT DEFAULT '',
                        video_title TEXT DEFAULT '',
                        details TEXT DEFAULT '{}',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # استعادة البيانات الصحيحة
                cursor.execute('''
                    INSERT INTO whisper_quality_logs
                    (timestamp, quality_score, quality_level, is_acceptable, video_id, video_title, details)
                    SELECT
                        COALESCE(timestamp, CURRENT_TIMESTAMP),
                        COALESCE(quality_score, 0.0),
                        COALESCE(quality_level, 'غير محدد'),
                        COALESCE(is_acceptable, 0),
                        COALESCE(video_id, ''),
                        COALESCE(video_title, ''),
                        COALESCE(details, '{}')
                    FROM whisper_quality_logs_backup
                ''')

                # حذف النسخة الاحتياطية
                cursor.execute('DROP TABLE whisper_quality_logs_backup')

                # إعادة إنشاء الفهارس
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_whisper_quality_video_id ON whisper_quality_logs(video_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_whisper_quality_timestamp ON whisper_quality_logs(timestamp)')

                conn.commit()
                logger.info("✅ تم إصلاح جدول whisper_quality_logs بنجاح")

        except Exception as e:
            logger.error(f"❌ فشل في إصلاح جدول whisper_quality_logs: {e}")

    def get_whisper_quality_stats(self, days: int = 7) -> Dict:
        """الحصول على إحصائيات جودة Whisper"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

                cursor.execute('''
                    SELECT
                        COUNT(*) as total_checks,
                        AVG(quality_score) as avg_quality_score,
                        SUM(CASE WHEN is_acceptable = 1 THEN 1 ELSE 0 END) as acceptable_count,
                        SUM(CASE WHEN quality_level = 'ممتاز' THEN 1 ELSE 0 END) as excellent_count,
                        SUM(CASE WHEN quality_level = 'جيد' THEN 1 ELSE 0 END) as good_count,
                        SUM(CASE WHEN quality_level = 'مقبول' THEN 1 ELSE 0 END) as acceptable_count_level,
                        SUM(CASE WHEN quality_level IN ('ضعيف', 'سيء جداً') THEN 1 ELSE 0 END) as poor_count
                    FROM whisper_quality_logs
                    WHERE DATE(timestamp) >= ?
                ''', (start_date,))

                stats = cursor.fetchone()

                total = stats[0] or 0

                return {
                    'total_quality_checks': total,
                    'average_quality_score': round(stats[1] or 0, 2),
                    'acceptable_transcripts': stats[2] or 0,
                    'excellent_transcripts': stats[3] or 0,
                    'good_transcripts': stats[4] or 0,
                    'acceptable_level_transcripts': stats[5] or 0,
                    'poor_transcripts': stats[6] or 0,
                    'acceptance_rate': round((stats[2] / total * 100) if total > 0 else 0, 2),
                    'excellence_rate': round((stats[3] / total * 100) if total > 0 else 0, 2)
                }

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات جودة Whisper: {e}")
            return {}

    def save_notification(self, notification_data: Dict) -> bool:
        """حفظ إشعار عام في قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO public_notifications (
                        type, video_id, title, url, duration, channel,
                        processed_at, extracted_text_length, status, message
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    notification_data.get('type'),
                    notification_data.get('video_id'),
                    notification_data.get('title'),
                    notification_data.get('url'),
                    notification_data.get('duration'),
                    notification_data.get('channel'),
                    notification_data.get('processed_at'),
                    notification_data.get('extracted_text_length'),
                    notification_data.get('status'),
                    notification_data.get('message', '')
                ))

                conn.commit()
                logger.debug("✅ تم حفظ الإشعار في قاعدة البيانات")
                return True

        except Exception as e:
            logger.error("❌ فشل في حفظ الإشعار", e)
            return False

    def get_latest_notifications(self, limit: int = 5) -> List[Dict]:
        """الحصول على آخر الإشعارات العامة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT type, video_id, title, url, duration, channel,
                           processed_at, status, created_at
                    FROM public_notifications
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (limit,))

                notifications = []
                for row in cursor.fetchall():
                    notifications.append({
                        'type': row[0],
                        'video_id': row[1],
                        'title': row[2],
                        'url': row[3],
                        'duration': row[4],
                        'channel': row[5],
                        'processed_at': row[6],
                        'status': row[7],
                        'created_at': row[8]
                    })

                return notifications

        except Exception as e:
            logger.error("❌ فشل في الحصول على الإشعارات", e)
            return []

    def save_indexing_stats(self, stats: Dict):
        """حفظ إحصائيات الفهرسة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # إنشاء جدول إحصائيات الفهرسة إذا لم يكن موجوداً
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS indexing_stats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        total_pages INTEGER DEFAULT 0,
                        indexed_pages INTEGER DEFAULT 0,
                        issues_found INTEGER DEFAULT 0,
                        issues_fixed INTEGER DEFAULT 0,
                        success_rate REAL DEFAULT 0.0,
                        last_check TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # إدراج الإحصائيات الجديدة
                cursor.execute('''
                    INSERT INTO indexing_stats
                    (total_pages, indexed_pages, issues_found, issues_fixed, success_rate, last_check)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    stats.get('total_pages', 0),
                    stats.get('indexed_pages', 0),
                    stats.get('issues_found', 0),
                    stats.get('issues_fixed', 0),
                    stats.get('success_rate', 0.0),
                    stats.get('last_check')
                ))

                conn.commit()
                logger.info("✅ تم حفظ إحصائيات الفهرسة")

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ إحصائيات الفهرسة: {e}")

    def save_redirect_rule(self, redirect_rule: Dict):
        """حفظ قاعدة إعادة التوجيه"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # إنشاء جدول قواعد إعادة التوجيه إذا لم يكن موجوداً
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS redirect_rules (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        from_url TEXT NOT NULL,
                        to_url TEXT NOT NULL,
                        redirect_type TEXT DEFAULT '301',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        is_active INTEGER DEFAULT 1
                    )
                ''')

                # إدراج قاعدة إعادة التوجيه
                cursor.execute('''
                    INSERT INTO redirect_rules (from_url, to_url, redirect_type, created_at)
                    VALUES (?, ?, ?, ?)
                ''', (
                    redirect_rule.get('from'),
                    redirect_rule.get('to'),
                    redirect_rule.get('type', '301'),
                    redirect_rule.get('created_at')
                ))

                conn.commit()
                logger.info(f"✅ تم حفظ قاعدة إعادة التوجيه: {redirect_rule.get('from')} -> {redirect_rule.get('to')}")

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ قاعدة إعادة التوجيه: {e}")

    def update_article_meta(self, article_id: str, meta_data: Dict):
        """تحديث البيانات الوصفية للمقال"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # إنشاء جدول البيانات الوصفية إذا لم يكن موجوداً
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS article_meta (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id TEXT NOT NULL,
                        canonical_tag TEXT,
                        canonical_url TEXT,
                        meta_description TEXT,
                        keywords TEXT,
                        structured_data TEXT,
                        seo_updated TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(article_id)
                    )
                ''')

                # تحديث أو إدراج البيانات الوصفية
                cursor.execute('''
                    INSERT OR REPLACE INTO article_meta
                    (article_id, canonical_tag, canonical_url, meta_description, keywords, structured_data, seo_updated)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    article_id,
                    meta_data.get('canonical_tag'),
                    meta_data.get('canonical_url'),
                    meta_data.get('meta_description'),
                    json.dumps(meta_data.get('keywords', []), ensure_ascii=False) if meta_data.get('keywords') else None,
                    json.dumps(meta_data.get('structured_data', {}), ensure_ascii=False) if meta_data.get('structured_data') else None,
                    meta_data.get('seo_updated')
                ))

                conn.commit()
                logger.info(f"✅ تم تحديث البيانات الوصفية للمقال: {article_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث البيانات الوصفية: {e}")

    def get_recent_articles(self, limit: int = 50) -> List[Dict]:
        """الحصول على المقالات الحديثة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM articles
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (limit,))

                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]

                articles = []
                for row in rows:
                    articles.append(dict(zip(columns, row)))

                return articles

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المقالات الحديثة: {e}")
            return []

    def get_article_by_id(self, article_id: str) -> Optional[Dict]:
        """الحصول على مقال بالمعرف"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('SELECT * FROM articles WHERE id = ?', (article_id,))
                row = cursor.fetchone()

                if row:
                    columns = [description[0] for description in cursor.description]
                    return dict(zip(columns, row))

                return None

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المقال: {e}")
            return None

    def save_pending_indexing_check(self, check_data: Dict):
        """حفظ فحص فهرسة معلق"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS pending_indexing_checks (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id TEXT NOT NULL,
                        check_data TEXT,
                        scheduled_time TEXT,
                        status TEXT DEFAULT 'pending',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                cursor.execute('''
                    INSERT INTO pending_indexing_checks
                    (article_id, check_data, scheduled_time, status)
                    VALUES (?, ?, ?, ?)
                ''', (
                    check_data['article_id'],
                    check_data['check_data'],
                    check_data['scheduled_time'],
                    check_data['status']
                ))

                conn.commit()

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الفحص المعلق: {e}")

    def save_seowl_stats(self, stats: Dict):
        """حفظ إحصائيات SEOwl"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS seowl_stats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        total_checks INTEGER DEFAULT 0,
                        successful_checks INTEGER DEFAULT 0,
                        failed_checks INTEGER DEFAULT 0,
                        issues_found INTEGER DEFAULT 0,
                        issues_fixed INTEGER DEFAULT 0,
                        last_check TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                cursor.execute('''
                    INSERT INTO seowl_stats
                    (total_checks, successful_checks, failed_checks, issues_found, issues_fixed, last_check)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    stats.get('total_checks', 0),
                    stats.get('successful_checks', 0),
                    stats.get('failed_checks', 0),
                    stats.get('issues_found', 0),
                    stats.get('issues_fixed', 0),
                    stats.get('last_check')
                ))

                conn.commit()

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ إحصائيات SEOwl: {e}")

    def save_indexing_request(self, request_data: Dict):
        """حفظ طلب فهرسة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS indexing_requests (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        url TEXT NOT NULL,
                        service TEXT,
                        status TEXT DEFAULT 'pending',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                cursor.execute('''
                    INSERT INTO indexing_requests (url, service, status, created_at)
                    VALUES (?, ?, ?, ?)
                ''', (
                    request_data['url'],
                    request_data['service'],
                    request_data['status'],
                    request_data['created_at']
                ))

                conn.commit()

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ طلب الفهرسة: {e}")

    def save_seo_improvement(self, improvement_data: Dict):
        """حفظ تحسين SEO مطلوب"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS seo_improvements (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        url TEXT NOT NULL,
                        improvement_type TEXT,
                        description TEXT,
                        status TEXT DEFAULT 'pending',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                cursor.execute('''
                    INSERT INTO seo_improvements
                    (url, improvement_type, description, status, created_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    improvement_data['url'],
                    improvement_data['improvement_type'],
                    improvement_data['description'],
                    improvement_data['status'],
                    improvement_data['created_at']
                ))

                conn.commit()

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ تحسين SEO: {e}")

    def save_performance_improvement(self, improvement_data: Dict):
        """حفظ تحسين أداء مطلوب"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS performance_improvements (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        url TEXT NOT NULL,
                        improvement_type TEXT,
                        description TEXT,
                        status TEXT DEFAULT 'pending',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                cursor.execute('''
                    INSERT INTO performance_improvements
                    (url, improvement_type, description, status, created_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    improvement_data['url'],
                    improvement_data['improvement_type'],
                    improvement_data['description'],
                    improvement_data['status'],
                    improvement_data['created_at']
                ))

                conn.commit()

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ تحسين الأداء: {e}")

    def save_link_fix_request(self, fix_data: Dict):
        """حفظ طلب إصلاح رابط"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS link_fix_requests (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        page_url TEXT NOT NULL,
                        broken_link TEXT,
                        alternative_url TEXT,
                        action TEXT,
                        status TEXT DEFAULT 'pending',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                cursor.execute('''
                    INSERT INTO link_fix_requests
                    (page_url, broken_link, alternative_url, action, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    fix_data['page_url'],
                    fix_data['broken_link'],
                    fix_data['alternative_url'],
                    fix_data['action'],
                    fix_data['status'],
                    fix_data['created_at']
                ))

                conn.commit()

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ طلب إصلاح الرابط: {e}")

    def save_manual_review_request(self, review_data: Dict):
        """حفظ طلب مراجعة يدوية"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS manual_review_requests (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        url TEXT NOT NULL,
                        issue_type TEXT,
                        description TEXT,
                        severity TEXT,
                        status TEXT DEFAULT 'pending',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                cursor.execute('''
                    INSERT INTO manual_review_requests
                    (url, issue_type, description, severity, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    review_data['url'],
                    review_data['issue_type'],
                    review_data['description'],
                    review_data['severity'],
                    'pending',
                    review_data['created_at']
                ))

                conn.commit()

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ طلب المراجعة اليدوية: {e}")

    def save_integration_stats(self, stats: Dict):
        """حفظ إحصائيات التكامل"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS integration_stats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        articles_checked INTEGER DEFAULT 0,
                        issues_found INTEGER DEFAULT 0,
                        issues_fixed INTEGER DEFAULT 0,
                        last_check TEXT,
                        integration_started TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                cursor.execute('''
                    INSERT INTO integration_stats
                    (articles_checked, issues_found, issues_fixed, last_check, integration_started)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    stats.get('articles_checked', 0),
                    stats.get('issues_found', 0),
                    stats.get('issues_fixed', 0),
                    stats.get('last_check'),
                    stats.get('integration_started')
                ))

                conn.commit()

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ إحصائيات التكامل: {e}")

    def get_pending_seo_improvements(self, limit: int = 50) -> List[Dict]:
        """الحصول على تحسينات SEO المعلقة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM seo_improvements
                    WHERE status = 'pending'
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (limit,))

                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]

                improvements = []
                for row in rows:
                    improvements.append(dict(zip(columns, row)))

                return improvements

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على تحسينات SEO: {e}")
            return []

    def get_pending_performance_improvements(self, limit: int = 50) -> List[Dict]:
        """الحصول على تحسينات الأداء المعلقة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM performance_improvements
                    WHERE status = 'pending'
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (limit,))

                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]

                improvements = []
                for row in rows:
                    improvements.append(dict(zip(columns, row)))

                return improvements

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على تحسينات الأداء: {e}")
            return []

    def get_pending_link_fixes(self, limit: int = 50) -> List[Dict]:
        """الحصول على إصلاحات الروابط المعلقة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM link_fix_requests
                    WHERE status = 'pending'
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (limit,))

                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]

                fixes = []
                for row in rows:
                    fixes.append(dict(zip(columns, row)))

                return fixes

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إصلاحات الروابط: {e}")
            return []

    def get_pending_manual_reviews(self, limit: int = 50) -> List[Dict]:
        """الحصول على المراجعات اليدوية المعلقة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM manual_review_requests
                    WHERE status = 'pending'
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (limit,))

                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]

                reviews = []
                for row in rows:
                    reviews.append(dict(zip(columns, row)))

                return reviews

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المراجعات اليدوية: {e}")
            return []

    def get_seowl_stats_history(self, limit: int = 30) -> List[Dict]:
        """الحصول على تاريخ إحصائيات SEOwl"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM seowl_stats
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (limit,))

                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]

                stats_history = []
                for row in rows:
                    stats_history.append(dict(zip(columns, row)))

                return stats_history

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على تاريخ إحصائيات SEOwl: {e}")
            return []

    def update_improvement_status(self, table_name: str, improvement_id: int, status: str):
        """تحديث حالة تحسين"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute(f'''
                    UPDATE {table_name}
                    SET status = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (status, improvement_id))

                conn.commit()

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث حالة التحسين: {e}")

# إنشاء مثيل عام لقاعدة البيانات
db = ArticleDatabase()
